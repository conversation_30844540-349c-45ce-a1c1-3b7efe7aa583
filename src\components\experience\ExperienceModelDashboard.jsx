'use client'
import React, {useEffect} from 'react'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import GUI from 'lil-gui'
import { useRef } from 'react'

export default function ExperienceModel({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()
  const {scene}=useThree() 
  const refModel=useRef(null)

  const handleLevelToHide = () => {
    try {
      if(experienceState?.levelToHide && scene) {
        const targetObject = scene.getObjectByName(experienceState?.levelToHide?.name);
        if (targetObject) {
          targetObject.traverse((child) => {
            if (child.isMesh) {
              child.visible = experienceState?.levelToHide?.toggleLevelState;
            }
          });
        }
      }
    } catch (error) {
      console.error('Error in handleLevelToHide:', error);
    }
  }

  useEffect(() => {
    try {
      if (refModel.current) {
        const gui = new GUI()
        gui.add(refModel.current.position, 'x', -80, 80, 0.001)
        gui.add(refModel.current.position, 'y', -80, 80, 0.001)
        gui.add(refModel.current.position, 'z', -80, 80, 0.001)
        return () => {
          gui.destroy()
        }
      }
    } catch (error) {
      console.error('Error setting up GUI:', error);
    }
  }, [])

  useEffect(() => {
    handleLevelToHide()
  }, [experienceState?.levelToHide?.toggleLevelState])
  
  return (
    <>
      <group
        ref={refModel}
        name="ExperienceModel"
        position={data?.position?.split(',').map(i=>Number(i)) || [0, 0, 0]}
      >
        {data?.modelsFiles && Array.isArray(data.modelsFiles) && data.modelsFiles.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        {data?.hideLevel && Array.isArray(data.hideLevel) && data.hideLevel.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        {data?.supportFiles && Array.isArray(data.supportFiles) && data.supportFiles.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        <group name="roomSnaps">
          {data?.roomSnaps && Array.isArray(data.roomSnaps) && data.roomSnaps.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
      </group>
      <Environment preset="city"/>
    </>
  )
}
