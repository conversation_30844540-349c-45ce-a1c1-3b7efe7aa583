'use client'
import React, { useRef, useEffect, useState } from 'react'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'

export default function ARSessionManagerNew({
  onSessionStart,
  onSessionEnd,
  onHitTestReady,
  onControllerSelect,
  children
}) {
  const refCurrentSession = useRef(null)
  const refHitTestSource = useRef(null)
  const controllerRef = useRef(null)
  const sessionStartTime = useRef(null)
  const { gl, scene, camera } = useThree()
  
  const [sessionData, setSessionData] = useState({
    isSessionActive: false,
    hitTestSource: null,
    hitTestReady: false,
    session: null
  })

  const startAr = async () => {
    console.log('🚀 ARSessionManagerNew: Attempting to start AR session...')
    console.log('🔍 ARSessionManagerNew: gl.xr available:', !!gl.xr)
    console.log('🔍 ARSessionManagerNew: gl.xr.enabled:', gl.xr?.enabled)
    console.log('🔍 Navigator.xr available:', !!navigator.xr)
    console.log('🔍 Current location protocol:', window.location.protocol)
    console.log('🔍 User agent:', navigator.userAgent.substring(0, 100))

    try {
      if (!navigator.xr) {
        console.warn('❌ ARSessionManagerNew: WebXR not supported on this browser.')
        console.warn('💡 WebXR requires HTTPS and a compatible browser (Chrome Android, Edge, etc.)')
        onSessionEnd?.()
        return
      }

      console.log('🔍 Checking AR session support...')
      const isSupported = await navigator.xr.isSessionSupported('immersive-ar')
      console.log('🔍 AR session supported:', isSupported)

      if (!isSupported) {
        console.warn('❌ ARSessionManagerNew: immersive-ar session not supported on this device.')
        console.warn('💡 AR requires a compatible device (Android with ARCore, iOS with ARKit)')
        onSessionEnd?.()
        return
      }

      // Try to request AR session with minimal features first
      let sessionConfig = {
        optionalFeatures: ['hit-test']
      }

      // Only add DOM overlay if we have a proper container
      const overlayContainer = document.getElementById('ar-overlay-container') || document.body
      if (overlayContainer) {
        sessionConfig.optionalFeatures.push('dom-overlay')
        sessionConfig.domOverlay = { root: overlayContainer }
      }

      console.log('🔧 ARSessionManagerNew: Requesting session with config:', sessionConfig)
      const session = await navigator.xr.requestSession('immersive-ar', sessionConfig)
      
      refCurrentSession.current = session
      sessionStartTime.current = Date.now()

      gl.xr.enabled = true

      // Set reference space type with safety check
      if (gl.xr.setReferenceSpaceType) {
        gl.xr.setReferenceSpaceType('local')
      } else {
        console.warn('ARSessionManagerNew: gl.xr.setReferenceSpaceType not available')
      }

      // Set the XR session with safety check
      if (gl.xr.setSession) {
        await gl.xr.setSession(session)
      } else {
        console.warn('ARSessionManagerNew: gl.xr.setSession not available, storing session manually')
        gl.xr._session = session
      }
      
      console.log('✅ ARSessionManagerNew: AR session started successfully.')

      // Set up controller
      let currentController = null
      if (gl.xr.getController) {
        const controller = gl.xr.getController(0)

        // Only proceed if controller is not null
        if (controller) {
          controllerRef.current = controller
          currentController = controller
          scene.add(controller)

          // Add controller event listeners
          controller.addEventListener('select', (event) => {
            console.log('👆 ARSessionManagerNew: Controller select event detected.')
            onControllerSelect?.(event, controller)
          })

          controller.addEventListener('selectstart', () => {
            console.log('👆 ARSessionManagerNew: Controller selectstart event detected.')
          })

          controller.addEventListener('selectend', () => {
            console.log('👆 ARSessionManagerNew: Controller selectend event detected.')
          })

          console.log('✅ ARSessionManagerNew: Controller set up successfully')
        } else {
          console.warn('ARSessionManagerNew: Controller is null, skipping event listeners')
        }
      } else {
        console.warn('ARSessionManagerNew: gl.xr.getController not available')
      }

      // Update session data
      setSessionData(prev => ({
        ...prev,
        isSessionActive: true,
        session: session,
        controller: currentController
      }))

      onSessionStart?.(session)

      // Set up session event listeners with null checks
      if (session && session.addEventListener) {
        session.addEventListener('end', (event) => {
          const sessionDuration = sessionStartTime.current ? Date.now() - sessionStartTime.current : 0
          console.log('🧹 ARSessionManagerNew: AR session ended.')
          console.log('🔍 Session duration:', sessionDuration + 'ms')
          console.log('🔍 Session end reason:', event)

          // Only end if session lasted more than 1 second (to avoid immediate failures)
          if (sessionDuration > 1000) {
            endAr()
          } else {
            console.warn('⚠️ AR session ended too quickly, might be a startup issue')
            endAr()
          }
        })

        session.addEventListener('inputsourceschange', (event) => {
          console.log('🎮 ARSessionManagerNew: Input sources changed:', event)
        })

        session.addEventListener('visibilitychange', (event) => {
          console.log('👁️ ARSessionManagerNew: Visibility changed:', event)
        })

        console.log('✅ ARSessionManagerNew: Session event listeners set up successfully')
      } else {
        console.warn('ARSessionManagerNew: Session is null or addEventListener not available')
      }

      // Set up hit testing when session starts
      if (gl.xr && gl.xr.addEventListener) {
        gl.xr.addEventListener('sessionstart', async () => {
          console.log('XR session STARTED successfully (via listener). Setting up hit-testing.')
          try {
            const session = gl.xr.getSession ? gl.xr.getSession() : null
            if (session && session.requestReferenceSpace) {
              const viewerReferenceSpace = await session.requestReferenceSpace('viewer')
              const hitTestSource = await session.requestHitTestSource({ space: viewerReferenceSpace })

              refHitTestSource.current = hitTestSource

              // Update session data with hit test source
              setSessionData(prev => ({
                ...prev,
                hitTestSource: hitTestSource,
                hitTestReady: true
              }))

              onHitTestReady?.(hitTestSource)
              console.log('✅ ARSessionManagerNew: Hit test source ready')
            } else {
              console.warn('ARSessionManagerNew: Session or requestReferenceSpace not available for hit testing')
            }
          } catch (error) {
            console.error('❌ ARSessionManagerNew: Failed to set up hit testing:', error)
          }
        })

        gl.xr.addEventListener('sessionend', (event) => {
          console.log('🧹 ARSessionManagerNew: XR session ended (via listener).')
          console.log('🔍 XR session end event:', event)
          endAr()
        })

        console.log('✅ ARSessionManagerNew: XR event listeners set up successfully')
      } else {
        console.warn('ARSessionManagerNew: gl.xr or addEventListener not available')
      }

    } catch (error) {
      console.error('❌ ARSessionManagerNew: Failed to start AR session:', error)
      console.error('❌ Error details:', error.message, error.stack)

      // Only call onSessionEnd if we're not already in the process of ending
      if (!refCurrentSession.current?.ended) {
        onSessionEnd?.()
      }
    }
  }

  const endAr = () => {
    console.log('🧹 ARSessionManagerNew: Ending AR session...')
    
    // Reset session data
    setSessionData({
      isSessionActive: false,
      hitTestSource: null,
      hitTestReady: false,
      session: null
    })
    
    if (refCurrentSession.current) {
      try {
        if (!refCurrentSession.current.ended) {
          refCurrentSession.current.end()
        }
        
        gl.xr.enabled = false

        // Clear the XR session with safety check
        if (gl.xr.setSession) {
          gl.xr.setSession(null)
        } else {
          gl.xr._session = null
        }

        gl.setAnimationLoop(null)
        
        refCurrentSession.current = null
        refHitTestSource.current = null

        // Clean up controller
        if (controllerRef.current) {
          scene.remove(controllerRef.current)
          controllerRef.current = null
        }

        console.log('✅ ARSessionManagerNew: AR session cleanup complete.')
        
      } catch (error) {
        console.error('❌ ARSessionManagerNew: Error during session cleanup:', error)
      }
    }
    
    onSessionEnd?.()
  }

  useEffect(() => {
    console.log('ARSessionManagerNew useEffect: Component MOUNTED.')

    // Add a small delay to ensure Canvas is properly initialized
    const timer = setTimeout(() => {
      console.log('ARSessionManagerNew: Starting AR session after Canvas initialization.')
      startAr()
    }, 100)

    return () => {
      console.log('ARSessionManagerNew useEffect: Component UNMOUNTED. Calling endAr().')
      clearTimeout(timer)
      endAr()
    }
  }, [])

  // Render children with session data
  return (
    <>
      {typeof children === 'function' ? children(sessionData) : children}
    </>
  )
}
