<<<<<<< HEAD
import React, { useEffect, useRef, useState } from 'react'
=======
import React, { useEffect, useRef, useState } from 'react' // eslint-disable-line no-unused-vars
>>>>>>> ar-xr
import * as THREE from 'three'
import { Html } from '@react-three/drei'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

<<<<<<< HEAD
// AR System Components
=======
// New AR System Components
>>>>>>> ar-xr
import ARSessionManagerNew from './ARSessionManagerNew'
import ARReticle, { CrosshairReticle, TargetReticle } from './ARReticle'
import ARController, { ARControllerVisual, ARTouchHandler } from './ARController'
import ExperienceModelAR from './ExperienceModelAR'
import ARDOMOverlay from './ARDOMOverlay'
<<<<<<< HEAD
import ExperienceAR from './ExperienceAR'


export default function ExperienceARWrapper({ data, useNewARSystem = true, onARSessionEnd }) {
  console.log('🔧 ExperienceARWrapper: Component mounting/rendering')

  const {experienceState}=useExperienceContext()
  console.log('🔧 ExperienceARWrapper: experienceState.modeAR =', experienceState?.modeAR)

  // Legacy AR state management (for old ExperienceAR component)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)
  const [arCompatible, setArCompatible] = useState(null) // null = checking, true = supported, false = not supported

  // Legacy refs for AR functionality
  const refModelAR = useRef(null)
  const refSessionAR = useRef(null)
  const refController = useRef(null)
  const refReticle = useRef(null)
  const glSession = useRef(null)
  const hitTestSource = useRef(null)
  const currentHitPose = useRef(null)

  // New AR System State
=======
import * as THREE from 'three'

export default function ExperienceARWrapper({ data }) {
  const {experienceState}=useExperienceContext()

  // State for UI elements and messages
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)

  // New AR System State
  // This component will now always use the new AR system (based on @react-three/xr)
>>>>>>> ar-xr
  const [arSessionData, setArSessionData] = useState(null)
  const [currentHitPose2, setCurrentHitPose2] = useState(null)
  const [placedModels, setPlacedModels] = useState([]) // Array of placed ExperienceModelAR instances
  const [reticleType, setReticleType] = useState('default') // 'default', 'crosshair', 'target'
  const [hitTestReady, setHitTestReady] = useState(false)
  const [sessionActive, setSessionActive] = useState(false)

<<<<<<< HEAD
  // Check AR compatibility on mount
  useEffect(() => {
    console.log('🔧 ExperienceARWrapper: useEffect for AR compatibility check running')

    const checkARSupport = async () => {
      console.log('🔍 ExperienceARWrapper: Checking AR compatibility...')

      if (!navigator.xr) {
        console.warn('❌ WebXR not available - this is expected on desktop/HTTP')
        console.warn('💡 For real AR testing, use HTTPS on a mobile device with ARCore/ARKit')

        // For development: simulate AR compatibility to test UI flow
        if (process.env.NODE_ENV === 'development') {
          console.log('🔧 Development mode: Simulating AR compatibility for UI testing')
          setArCompatible(true)
          return
        }

        setArCompatible(false)
        return
      }

      try {
        const supported = await navigator.xr.isSessionSupported('immersive-ar')
        console.log('🔍 AR session supported:', supported)
        setArCompatible(supported)
      } catch (error) {
        console.error('❌ Error checking AR support:', error)
        setArCompatible(false)
      }
    }

    checkARSupport()
  }, [])

  const resetModelPlacement = () => {
    console.log('Resetting model placement')
    setShowModel(false)
    if (refModelAR.current) {
      refModelAR.current.visible = false
    }
    if (refReticle.current) {
      refReticle.current.visible = false
    }
=======
  const resetModelPlacement = () => { // This function should be passed down to ExperienceAR
    console.log('ExperienceARWrapper: Resetting model placement (clearing placedModels)')
    setPlacedModels([])
    // ExperienceAR will handle hiding reticle/model visibility
>>>>>>> ar-xr
  }

  // New AR System Handlers
  const handleARSessionStart = (session) => {
    console.log('🚀 New AR session started:', session)
    setSessionActive(true)
  }

  const handleARSessionEnd = () => {
    console.log('🛑 New AR session ended')
    setCurrentHitPose2(null)
    setPlacedModels([])
    setSessionActive(false)
    setHitTestReady(false)
    // Call external handler to update experience state
    onARSessionEnd?.()
  }

  const handleHitTestReady = (hitTestSource) => {
    console.log('🎯 Hit test source ready:', hitTestSource)
    console.log('🎯 Hit test source type:', typeof hitTestSource)
    setHitTestReady(true)
  }

  const handleHitPoseUpdate = (hitPose) => {
    console.log('🎯 Hit pose updated:', !!hitPose)
    setCurrentHitPose2(hitPose)
  }

  const handleControllerSelect = (event, controller) => {
    console.log('🎮 Controller select triggered in ExperienceWorld')
    console.log('🎮 Event:', event)
    console.log('🎮 Controller:', controller)
    console.log('🎮 currentHitPose2:', !!currentHitPose2)
    console.log('🎮 currentHitPose2 data:', currentHitPose2)

    if (currentHitPose2 && currentHitPose2.transform && currentHitPose2.transform.matrix) {
      console.log('✅ Using hit-test position for model placement')

      // Extract position and rotation from hit pose
      const matrix = new THREE.Matrix4().fromArray(currentHitPose2.transform.matrix)
      const position = new THREE.Vector3()
      const rotation = new THREE.Euler()
      const scale = new THREE.Vector3()

      matrix.decompose(position, new THREE.Quaternion(), scale)

      console.log('🎯 Hit-test position:', {
        x: position.x.toFixed(3),
        y: position.y.toFixed(3),
        z: position.z.toFixed(3)
      })

      // Create new model placement at exact hit-test location
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [position.x, position.y, position.z],
        rotation: [0, 0, 0], // Keep building upright
        scale: [0.3, 0.3, 0.3], // Smaller scale for better AR viewing
        hitPose: currentHitPose2,
        createdAt: Date.now(),
        placementType: 'hit-test'
      }

      // Add to placed models (limit to 5 models max)
      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 5) {
          updated.shift() // Remove oldest model
        }
        return updated
      })

      console.log('� Placed ExperienceModelAR at:', position)
      console.log('🏠 Total placed models:', placedModels.length + 1)
    } else {
      console.warn('⚠️ No hit pose available, placing at default position')

      // Fallback: place model at default position for testing
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [0, -0.5, -1], // Default position in front of camera, slightly below
        rotation: [0, 0, 0],
        scale: [0.3, 0.3, 0.3], // Smaller scale to match hit-test placement
        hitPose: null,
        createdAt: Date.now(),
        placementType: 'fallback'
      }

      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 5) {
          updated.shift()
        }
        return updated
      })

      console.log('🏠 Placed ExperienceModelAR at default position')
      console.log('🏠 Total placed models:', placedModels.length + 1)
    }
  }

  const handleModelRemoved = (modelId) => {
    setPlacedModels(prev => prev.filter(model => model.id !== modelId))
    console.log('➖ Model removed:', modelId)
  }

  const clearAllModels = () => {
    setPlacedModels([])
    console.log('🧹 All models cleared')
  }

  const cycleReticleType = () => {
    const types = ['default', 'crosshair', 'target']
    const currentIndex = types.indexOf(reticleType)
    const nextIndex = (currentIndex + 1) % types.length
    setReticleType(types[nextIndex])
    console.log('🎯 Reticle type changed to:', types[nextIndex])
  }

<<<<<<< HEAD
  // Show AR compatibility message if not supported
  if (experienceState?.modeAR && arCompatible === false) {
    return (
      <Html position={[0, 0, -2]} center>
        <div className="bg-red-600 text-white p-6 rounded-lg max-w-md text-center">
          <h2 className="text-xl font-bold mb-3">AR Not Supported</h2>
          <p className="mb-3 text-sm">
            WebXR AR is not supported on this device or browser.
          </p>
          <p className="text-xs text-red-200 mb-4">
            AR requires HTTPS, compatible browser (Chrome on Android), and ARCore support.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm"
          >
            Reload Page
          </button>
        </div>
      </Html>
    )
  }

  // Show loading while checking AR compatibility
  if (experienceState?.modeAR && arCompatible === null) {
    return (
      <Html position={[0, 0, -2]} center>
        <div className="bg-black/80 text-white p-6 rounded-lg text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-3"></div>
          <p className="text-sm">Checking AR compatibility...</p>
        </div>
      </Html>
    )
  }

  // Only render AR components if in AR mode
  if (!experienceState?.modeAR) {
    return null
  }

  return (
    useNewARSystem ? (
      arCompatible ? (
        // New AR System - Real AR
        <ARSessionManagerNew
          onSessionStart={handleARSessionStart}
          onSessionEnd={handleARSessionEnd}
          onHitTestReady={handleHitTestReady}
          onControllerSelect={handleControllerSelect}
        >
          {(sessionData) => (
            <>
              {/* Lighting for AR objects */}
              <ambientLight intensity={0.6} />
              <directionalLight position={[10, 10, 5]} intensity={0.8} />
=======
  return (
    // This component now directly renders the AR system using ARSessionManagerNew
    // and passes down the necessary state and handlers.
    <ARSessionManagerNew
      onSessionStart={handleARSessionStart}
      onSessionEnd={handleARSessionEnd}
      onHitTestReady={handleHitTestReady}
      onControllerSelect={handleControllerSelect}
    >
      {(sessionData) => (
        <>
          {/* Lighting for AR objects */}
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={0.8} />
>>>>>>> ar-xr

              {/* AR Reticle */}
              <group>
                {/* Always visible test reticle */}
                <mesh position={[0, -0.5, -1]} rotation-x={-Math.PI / 2}>
                  <ringGeometry args={[0.1, 0.15, 32]} />
                  <meshBasicMaterial
                    color="#00ff00"
                    transparent={true}
                    opacity={0.9}
                    side={THREE.DoubleSide}
                    depthTest={false}
                    depthWrite={false}
                  />
                </mesh>

                {sessionData.hitTestReady ? (
                  <>
                    {reticleType === 'default' && (
                      <ARReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                      visible={true} // ARReticle component manages its own visibility based on hitPose
                      />
                    )}
                    {reticleType === 'crosshair' && (
                      <CrosshairReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                      visible={true}
                      />
                    )}
                    {reticleType === 'target' && (
                      <TargetReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                      visible={true}
                      />
                    )}
                  </>
                ) : (
                  // Fallback reticle when hit test is not ready
                  <mesh position={[0, 0, -1]}>
                    <ringGeometry args={[0.1, 0.15, 32]} />
                    <meshBasicMaterial
                      color="#ff0000"
                      transparent={true}
                      opacity={0.8}
                      side={THREE.DoubleSide}
                    />
                  </mesh>
                )}
              </group>

              {/* AR Controller */}
              <ARController
                onSelect={handleControllerSelect}
                enabled={sessionData.isSessionActive}
              />

              {/* AR Controller Visual */}
              <ARControllerVisual
                controllerIndex={0}
                showRay={true}
                rayColor="#ffffff"
                rayLength={5}
              />

              {/* Placed ExperienceModelAR instances */}
              {placedModels.map((model) => (
                <group
                  key={model.id}
                  position={model.position}
                  rotation={model.rotation}
                  scale={model.scale}
                >
                  <ExperienceModelAR
                    data={data}
                    modelPlacementMatrix={new THREE.Matrix4().compose(new THREE.Vector3(...model.position), new THREE.Quaternion().setFromEuler(new THREE.Euler(...model.rotation)), new THREE.Vector3(...model.scale)).elements}
                  />
                </group>
              ))}

              {/* Touch Handler for mobile */}
              <ARTouchHandler
                onTap={handleControllerSelect}
                enabled={sessionData.isSessionActive}
              />

              {/* Debug click handler */}
              <mesh
                position={[0, 0, -0.1]}
                onClick={handleControllerSelect}
                onPointerDown={handleControllerSelect}
              >
                <boxGeometry args={[0.1, 0.1, 0.1]} />
                <meshBasicMaterial
                  color="#ff00ff"
                  transparent={true}
                  opacity={0.5}
                />
              </mesh>
            </>
<<<<<<< HEAD
          )}
        </ARSessionManagerNew>
      ) : (
        // AR Not Compatible - Show fallback message and demo objects
        <>
          <ambientLight intensity={0.8} />
          <directionalLight position={[10, 10, 5]} intensity={1.0} />

          {/* Fallback message */}
          <Html position={[0, 2, -2]} center>
            <div className="bg-yellow-600 text-white p-4 rounded-lg max-w-md text-center">
              <h3 className="font-bold mb-2">AR Not Available</h3>
              <p className="text-sm mb-2">
                WebXR AR requires HTTPS and a compatible mobile device.
              </p>
              <p className="text-xs">
                For testing: Use HTTPS on Android (ARCore) or iOS (ARKit)
              </p>
            </div>
          </Html>

          {/* Demo objects to show AR would work */}
          <group position={[0, -0.5, -1]}>
            <ExperienceModelAR data={data} refModelAR={null} />
          </group>

          <group position={[1, -0.5, -1]}>
            <ExperienceModelAR data={data} refModelAR={null} />
          </group>

          <group position={[-1, -0.5, -1]}>
            <ExperienceModelAR data={data} refModelAR={null} />
          </group>
        </>
      )
    ) : (
        // Legacy AR System
        <ExperienceAR
          data={data}
          refModelAR={refModelAR}
          refSessionAR={refSessionAR}
          refController={refController}
          refReticle={refReticle}
          glSession={glSession}
          hitTestSource={hitTestSource}
          currentHitPose={currentHitPose}
          setErrorMessage={setErrorMessage}
          setShowError={setShowError}
          showModel={showModel}
          setShowModel={setShowModel}
        />
=======
        )}
>>>>>>> ar-xr
      )
  )
}
