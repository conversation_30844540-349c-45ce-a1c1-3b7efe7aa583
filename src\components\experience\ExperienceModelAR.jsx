// ExperienceModelAR.jsx
'use client'
import React, {useEffect, useCallback, useState, useRef} from 'react'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import * as THREE from 'three'

export default function ExperienceModelAR({ data, refModelAR, modelPlacementMatrix }) {
  const {experienceState}=useExperienceContext()
  const {scene}=useThree()
  const refHideLevel=useRef(null)
  const [levelsToHideList, setLevelsToHideList]=useState([])

  console.log('ExperienceModelAR: Component RENDERING.');

  const handleLevelToHde = () => { /* ... */ }

  useEffect(() => {
    console.log('ExperienceModelAR: useEffect (mount/initial setup) fired.');
    // Note: refModelAR.current might be null here since the ref is attached during render
    // We'll handle matrixAutoUpdate in the modelPlacementMatrix useEffect instead
    
    const hideList=scene.getObjectByName('hideLevel')?.children
    if (data?.hideLevel && hideList) {
      data.hideLevel.forEach((i) => {
        const found = hideList.find(({name}) => i?.name === name);
        if (found) found.priority = i?.priority || 0;
      });
    }
    setLevelsToHideList(hideList);
  }, [])

  useEffect(() => {
    console.log('ExperienceModelAR: useEffect (levelToHide) fired.');
    handleLevelToHde()
  }, [experienceState?.levelToHide])

  useEffect(() => {
    console.log('ExperienceModelAR: useEffect (modelPlacementMatrix) fired.');
    if (refModelAR?.current && modelPlacementMatrix) {
      console.log('ExperienceModelAR: Applying modelPlacementMatrix and making visible.');
      // Set matrixAutoUpdate to false before applying matrix
      refModelAR.current.matrixAutoUpdate = false;
      refModelAR.current.matrix.fromArray(modelPlacementMatrix);
      refModelAR.current.updateMatrixWorld(true);

      const position = new THREE.Vector3();
      const quaternion = new THREE.Quaternion();
      const scale = new THREE.Vector3();
      refModelAR.current.matrix.decompose(position, quaternion, scale);

      console.log('ExperienceModelAR Debug: Model Position (from matrix):', position.toArray());
      console.log('ExperienceModelAR Debug: Model Scale (from matrix):', scale.toArray());
      console.log('ExperienceModelAR Debug: isVisible (refModelAR.current):', refModelAR.current.visible);
      console.log('ExperienceModelAR Debug: Actual Component Scale Prop (aRscale):', experienceState?.aRscale);

      // Debug box for visual verification
      if (!refModelAR.current.getObjectByName('debugBox')) {
        const debugBox = new THREE.Mesh(
          new THREE.BoxGeometry(0.1, 0.1, 0.1),
          new THREE.MeshBasicMaterial({ color: 0x00ffff, wireframe: true })
        );
        debugBox.name = 'debugBox';
        refModelAR.current.add(debugBox);
        console.log('ExperienceModelAR Debug: Added debug box to model group.');
      }
    } else {
      console.warn('ExperienceModelAR: modelPlacementMatrix useEffect skipped. refModelAR.current:', refModelAR?.current, 'modelPlacementMatrix:', modelPlacementMatrix);
    }
  }, [modelPlacementMatrix, refModelAR]);

  return (
    <>
      <group
        ref={refModelAR}
        name="ExperienceModelAR"
        scale={[experienceState?.aRscale || 1, experienceState?.aRscale || 1, experienceState?.aRscale || 1]}
      >
        {console.log('ExperienceModelAR: Inside group render. Current scale prop:', experienceState?.aRscale)}
        {/* Temporary debug mesh */}
        <mesh>
          <boxGeometry args={[0.5, 0.5, 0.5]} />
          <meshNormalMaterial />
        </mesh>
        {/* Original GLTF Loaders */}
        {data?.modelsFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
         <group ref={refHideLevel} name="hideLevel">
          {data?.hideLevel?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
        {data?.supportFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        <group name="roomSnaps">
          {data?.roomSnaps?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
      </group>
      <Environment preset="city"/>
    </>
  )
}
