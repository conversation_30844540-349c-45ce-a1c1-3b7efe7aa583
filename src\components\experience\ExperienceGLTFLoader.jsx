'use client'
import React from 'react'
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader'
import {DRACOLoader} from 'three/examples/jsm/loaders/DRACOLoader'
import { useLoader } from '@react-three/fiber'

export default function ExperienceGLTFLoader({path}) {
    // console.log('ExpWorldObj ExpWorldGltfLoader -',path)

    // Safety check for path
    if (!path?.url) {
        console.warn('ExperienceGLTFLoader: No valid path.url provided:', path)
        return null
    }

    try {
        const model=useLoader(GLTFLoader, path.url,(loader)=>{
            const dracoLoader=new DRACOLoader()
            dracoLoader.setDecoderPath('/draco/')
            loader.setDRACOLoader(dracoLoader)
        })

        // Safety check for model
        if (!model?.scene) {
            console.warn('ExperienceGLTFLoader: No scene found in model:', path.url)
            return null
        }

        return (
            <group name={path?.name || 'unnamed-model'}>
                <primitive object={model.scene}/>
            </group>
        )
    } catch (error) {
        console.error('ExperienceGLTFLoader: Error loading model:', path.url, error)
        return null
    }
}
