'use client';
import React from 'react';

class CameraControlsErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error details
    console.error('Camera Controls Error:', error);
    console.error('Error Info:', errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <div className="absolute top-4 right-4 bg-red-600 text-white p-4 rounded-lg z-50 max-w-md">
          <h3 className="font-bold text-lg mb-2">Camera Controls Error</h3>
          <p className="text-sm mb-2">
            An error occurred in the camera controls. The scene may still be interactive.
          </p>
          <button
            onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
            className="bg-red-800 hover:bg-red-900 px-3 py-1 rounded text-sm"
          >
            Retry
          </button>
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-2">
              <summary className="cursor-pointer text-sm">Error Details</summary>
              <pre className="text-xs mt-1 overflow-auto max-h-32">
                {this.state.error && this.state.error.toString()}
<<<<<<< HEAD
                {this.state.errorInfo && this.state.errorInfo.componentStack}
=======
                {this.state.errorInfo?.componentStack && `\n${this.state.errorInfo.componentStack}`}
>>>>>>> ar-xr
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default CameraControlsErrorBoundary;
