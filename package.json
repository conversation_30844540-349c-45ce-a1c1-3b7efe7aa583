{"name": "luyarisitetemplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p3002 --experimental-https", "build": "next build", "start": "next start -p3002", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^3.9.1", "@react-three/drei": "^10.0.0-rc.3", "@react-three/fiber": "^9.0.0-rc.10", "@react-three/xr": "^6.6.17", "@types/three": "^0.177.0", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "firebase": "^11.8.1", "framer-motion": "^12.16.0", "leva": "^0.10.0", "lil-gui": "^0.20.0", "mongodb": "^6.17.0", "mongoose": "^8.15.1", "motion": "^12.16.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "three": "^0.177.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4"}}