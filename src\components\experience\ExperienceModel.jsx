'use client'
import React, {useEffect, useCallback, useState, useRef} from 'react'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

export default function ExperienceModel({data}) {
  const {experienceState}=useExperienceContext() // experienceDispatch is not used here
  const {scene}=useThree() 
  const refModel=useRef(null)
  const refHideLevel=useRef(null)
  const [levelsToHideList, setLevelsToHideList]=useState([])

  /**
   * Smart Level Visibility Management
   * Implements priority-based visibility control for hideLevel objects
   */
  const hideGroup = scene.getObjectByName('hideLevel');

  const handleLevelToHde = () => {
    let priorityVisibleList=[]
    let priorityInvisibleList=[]
    if(experienceState?.levelToHide && levelsToHideList && Array.isArray(levelsToHideList)){
      levelsToHideList.forEach((i)=>{
        if(i?.visible){
          priorityVisibleList.push(i?.priority || 0)
        }else{
          priorityInvisibleList.push(i?.priority || 0)
        }
      })

      if (priorityInvisibleList.length > 0 || priorityVisibleList.length > 0) {
        const minPriority = priorityInvisibleList.length > 0 ? Math.min(...priorityInvisibleList) : 0
        const maxPriority = priorityVisibleList.length > 0 ? Math.max(...priorityVisibleList) : 0
        const targetObject = hideGroup?.getObjectByName(experienceState?.levelToHide?.name);

        if (targetObject) {
          levelsToHideList.find(({name,priority})=>{
            if(name===experienceState?.levelToHide?.name){
              if(priority===minPriority){
                targetObject.visible = true;
              }else if(priority===maxPriority){
                targetObject.visible = false;
              }
            }
          })
        }
      }
    }
  }
  
  useEffect(() => {
    const hideList=scene.getObjectByName('hideLevel')?.children
    if (data?.hideLevel && hideList) {
      data.hideLevel.forEach((i) => {
        const found = hideList.find(({name}) => i?.name === name);
        if (found) {
          found.priority = i?.priority || 0;
        }
      });
    }
    setLevelsToHideList(hideList || [])
  }, [])
  
  useEffect(() => {
    handleLevelToHde()
  }, [experienceState?.levelToHide])

<<<<<<< HEAD
  console.log('ExperienceModel Debug:', {
    position: refModel.current?.position,
    modelsFilesLength: data?.modelsFiles?.length,
    hideLevelLength: data?.hideLevel?.length,
    supportFilesLength: data?.supportFiles?.length,
    roomSnapsLength: data?.roomSnaps?.length,
    hasData: !!data
  })
=======
  // console.log('ExperienceModel:',refModel.current?.position)
>>>>>>> ar-xr
  
  return (
    <>
      <group 
        name="ExperienceModel"
        ref={refModel}
        position={data?.position?.split(',').map(i=>Number(i))}
      >
        {data?.modelsFiles && Array.isArray(data.modelsFiles) && data.modelsFiles.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
         <group ref={refHideLevel} name="hideLevel">
          {data?.hideLevel && Array.isArray(data.hideLevel) && data.hideLevel.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
        {data?.supportFiles && Array.isArray(data.supportFiles) && data.supportFiles.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        <group name="roomSnaps">
          {data?.roomSnaps && Array.isArray(data.roomSnaps) && data.roomSnaps.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
      </group>
      <Environment preset="city"/>
    </>
  )
}
