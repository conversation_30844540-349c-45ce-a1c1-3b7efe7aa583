// ExperienceWorld.jsx
'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense } from 'react'
import ExperienceAR from './ExperienceAR' // This is now the entry point for AR
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'

export default function ExperienceWorld({data}) {
  const {experienceState, experienceDispatch} = useExperienceContext()

  const handleARSessionEnd = () => {
    console.log("ARSession ended, dispatching ACTIONS_EXPERIENCE.MODE_AR_OFF");
    experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_AR_OFF});
  };

  console.log('ExperienceWorld: Rendering. modeAR:', experienceState?.modeAR);

  return (
    <>
      {experienceState?.modeAR ? (
        <>
          <Canvas
            key="ar-canvas"
            onCreated={({ gl }) => {
              gl.xr.enabled = true
            }}
            gl={{
              antialias: true,
              alpha: true,
              preserveDrawingBuffer: true,
              powerPreference: "high-performance"
            }}
            camera={{
              position: [0, 1.6, 3],
              fov: 75,
              near: 0.1,
              far: 1000
            }}
            dpr={[1, 2]}
            linear={true}
          >
            <Suspense fallback={<LoadingSpinner/>}>
              <ExperienceAR data={data} />
            </Suspense>
          </Canvas>
          <div className="p-4 bg-gray-900 text-white text-sm">
            <h3 className="font-medium mb-2">Instructions:</h3>
            <ul className="space-y-1 opacity-80">
              <li>• Tap screen or trigger to place cube at hit-test location</li>
              <li>• Click cube to change material/color</li>
              <li>• Use scale slider to resize selected cube (25%-100%)</li>
              <li>• White dot toggles cube visibility</li>
            </ul>
          </div>
        </>
      ) : (
        // Regular Mode Canvas - with camera controls
        <CameraControlsErrorBoundary>
          <Canvas
            key="regular-canvas"
            gl={{
              antialias: true,
              alpha: true,
              powerPreference: "high-performance"
            }}
            camera={{
              position: [0, 1.6, 3],
              fov: 75,
              near: 0.1,
              far: 1000
            }}
            dpr={[1, 2]}
            linear={true}
          >
            <Suspense fallback={<LoadingSpinner/>}>
              <ExperienceOrbitControls data={data}/>
              {experienceState?.mode360 && <Experience360 data={data}/>}
              {experienceState?.modeModel && <ExperienceModel data={data}/>}
            </Suspense>
          </Canvas>
        </CameraControlsErrorBoundary>
      )}
    </>
  )
}
