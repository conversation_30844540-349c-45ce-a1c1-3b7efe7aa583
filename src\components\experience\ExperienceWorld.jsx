// ExperienceWorld.jsx
'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect, useState } from 'react'
import ExperienceAR from './ExperienceAR'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import ARErrorBoundary from './ARErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'

const DEBUG_AR_POLYFILLS = false; // Set to true to see polyfill warnings, false to suppress

export default function ExperienceWorld({data}) {
  const {experienceState, experienceDispatch} = useExperienceContext() // Ensure setExperienceState is available from your context

  const handleARSessionEnd = () => {
    console.log("ARSession ended, dispatching ACTIONS_EXPERIENCE.MODE_AR_OFF");
    experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_AR_OFF});
  };

  // console.log('ExperienceWorld: Rendering. modeAR:', experienceState?.modeAR);

  return (
    <CameraControlsErrorBoundary>
      <Canvas>
        <Suspense fallback={<LoadingSpinner/>}>
          {experienceState?.modeAR ? (
              <ExperienceAR
                data={data}
                onARSessionEnd={handleARSessionEnd} // Pass the handler to ExperienceAR
              />
            )
          : (
              <>
                <ExperienceOrbitControls data={data}/>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
              </>
            )
          }
        </Suspense>
      </Canvas>
    </CameraControlsErrorBoundary>
  )
}
