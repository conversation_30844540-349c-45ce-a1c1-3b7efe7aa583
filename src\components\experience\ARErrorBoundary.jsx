'use client';
import React from 'react';

class ARErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error details
    console.error('AR System Error:', error);
    console.error('Error Info:', errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI for AR errors
      return (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 text-white z-50">
          <div className="text-center p-8 max-w-md">
            <h2 className="text-2xl font-bold mb-4 text-red-400">AR System Error</h2>
            <p className="mb-4">
              An error occurred while loading the AR system. This might be due to:
            </p>
            <ul className="text-sm text-gray-300 mb-6 text-left">
              <li>• WebXR compatibility issues</li>
              <li>• Browser security restrictions</li>
              <li>• Device hardware limitations</li>
              <li>• Network connectivity problems</li>
            </ul>
            <div className="space-y-3">
              <button
                onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
                className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg block w-full"
              >
                Retry AR
              </button>
              <button
                onClick={() => window.location.reload()}
                className="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded-lg block w-full"
              >
                Reload Page
              </button>
            </div>
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-yellow-400">
                  Developer Error Details
                </summary>
                <pre className="text-xs mt-2 overflow-auto max-h-32 bg-black/50 p-2 rounded">
                  {this.state.error && this.state.error.toString()}
                  {this.state.errorInfo && this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ARErrorBoundary;
