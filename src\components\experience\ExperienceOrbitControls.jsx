'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { MathUtils } from 'three';
import { useThree } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import * as THREE from 'three';

export default function ExperienceOrbitControls({ data }) {
  const { experienceState } = useExperienceContext();
  const refControls = useRef(null);
  const { camera, scene, invalidate } = useThree();
  const [snapObjects, setSnapObjects] = useState(null); // Initialize with null
  const [snapObject, setSnapObject] = useState(null); // Initialize with null

  const handleSnapViewPoints = useCallback(() => {
    if (!refControls.current || !snapObjects) return;

    let targetSnapPoint = null;
    if (snapObjects && experienceState?.activeRoomSnap) {
      targetSnapPoint = snapObjects.children.find(({ name }) => {
        return name === experienceState.activeRoomSnap;
      });
    }

    if (targetSnapPoint) {
      // Find the mesh child within the targetSnapPoint
      let meshChild = null;
      targetSnapPoint.traverse((child) => {
        if (child.isMesh) {
          meshChild = child;
        }
      });
      setSnapObject(meshChild); // Update the snapObject state
    } else {
      setSnapObject(null); // Clear snapObject if no target found
    }

    if (snapObject && refControls.current) {
      // Calculate the world position and quaternion of the snapObject
      const worldPosition = new THREE.Vector3();
      const worldQuaternion = new THREE.Quaternion();
      snapObject.getWorldPosition(worldPosition);
      snapObject.getWorldQuaternion(worldQuaternion);

      // Set the OrbitControls target to the snapObject's position
      refControls.current.target.copy(worldPosition);

      // Calculate the new camera position based on the snapObject's position and orientation
      // For a "look-in-direction" effect, we can place the camera slightly behind the snapObject
      // along its forward vector.
      const forwardVector = new THREE.Vector3(0, 0, 1); // Z-axis is typically forward
      forwardVector.applyQuaternion(worldQuaternion); // Rotate the forward vector by snapObject's orientation

      // Adjust distance from the snapObject as needed for the camera's new position
      const distance = experienceState?.firstPersonView ? 0.05 : 1; // Small distance for first person, more for general view
      const newCameraPosition = new THREE.Vector3().copy(worldPosition).add(forwardVector.multiplyScalar(distance));

      // Set the camera's position
      camera.position.copy(newCameraPosition);

      // Make the camera look at the snapObject's position
      camera.lookAt(worldPosition);

      // Update controls to reflect the changes
      refControls.current.update();
      invalidate(); // Request a render frame
    }
  }, [experienceState?.activeRoomSnap, snapObjects, snapObject, camera, invalidate]);

  useEffect(() => {
    // Only call handleSnapViewPoints if snapObjects is not null (meaning it has been set initially)
    if (snapObjects !== null) {
      handleSnapViewPoints();
    }
  }, [experienceState?.activeRoomSnap, snapObjects, handleSnapViewPoints]);

  useEffect(() => {
    const roomSnapsObjects = scene.getObjectByName('roomSnaps');
    setSnapObjects(roomSnapsObjects);
    // Initially set target to origin if no snap point is active
    if (!experienceState?.activeRoomSnap && refControls.current) {
      refControls.current.target.copy(new THREE.Vector3(0, 0, 0));
      refControls.current.update();
    }
  }, [scene, experienceState?.activeRoomSnap]);

  // Safe parsing of distance values from data
  const minDistance = experienceState?.firstPersonView ? 0.0 : (parseFloat(data?.minDistance) || 1);
  const maxDistance = experienceState?.firstPersonView ? 0.5 : (parseFloat(data?.maxDistance) || 50);

  // Debug logging only when controls are properly initialized
  useEffect(() => {
    if (refControls.current) {
      // console.log('ExperienceOrbitControls initialized:', {
      //   target: refControls.current.target,
      //   minDistance: refControls.current.minDistance,
      //   maxDistance: refControls.current.maxDistance,
      //   cameraPosition: refControls.current.object?.position
      // });
    }
  }, [refControls.current]);

  return (
    <>
      <OrbitControls
        ref={refControls}
        minDistance={experienceState?.firstPersonView ? 0 : minDistance}
        maxDistance={experienceState?.firstPersonView ? 0.5 : maxDistance}
        maxPolarAngle={experienceState?.firstPersonView ? MathUtils.degToRad(135) : MathUtils.degToRad(85)}
        minPolarAngle={experienceState?.firstPersonView ? MathUtils.degToRad(45) : MathUtils.degToRad(0)}
        rotateSpeed={-0.25}
        enablePan={false}
        enableDamping={true}
        dampingFactor={0.05}
      />
    </>
  );
}